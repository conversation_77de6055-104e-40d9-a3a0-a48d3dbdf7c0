import sys
from pathlib import Path

from rclone_python import rclone

# Add the parent directory to the path to access the code folder
sys.path.append(str(Path(__file__).parent.parent))


def download_labels():
    """Download all labels (CSV files only) from the labels folder."""

    # Get the project root directory
    project_root = Path(__file__).parent.parent
    labels_dir = project_root / 'data' / 'labels'

    # Create the labels directory if it doesn't exist
    labels_dir.mkdir(parents=True, exist_ok=True)

    print(str(labels_dir.as_posix()))

    # Download CSV files only from the remote location
    rclone.copy(
        'wasabi-eu:helio.vanasseldonkmushrooms/labels',
        str(labels_dir.as_posix()),
        args=['--include', '*.csv'],
        show_progress=True,
    )
    print('Download completed!')

    # # Count label files
    # total_files = 0
    # for location in labels_dir.iterdir():
    #     if location.exists() and location.is_dir():
    #         csv_files = list(location.rglob('*.csv'))
    #         print(f'{location.name}: {len(csv_files)} CSV files')
    #         total_files += len(csv_files)

    # print(f'Total CSV files: {total_files}')


if __name__ == '__main__':
    download_labels()
