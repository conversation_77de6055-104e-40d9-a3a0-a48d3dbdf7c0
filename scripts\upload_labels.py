import glob
import os
import tempfile

from rclone_python import rclone

location = 'mutshoek' if 'mutshoek' in os.uname().nodename else 'vosdeel'
print(f'This is {location!r}')

print('Uploading labels...')
rclone.copy(
    '/mnt/sda/images/',
    f'wasabi-eu:helio.vanasseldonkmushrooms/labels/{location}',
    args=['--include', '*.csv'],
    show_progress=True,
)

print('Parsing labels...')
image_files = []
for file in glob.glob('/mnt/sda/images/**/*.csv', recursive=True):
    dir_name = os.path.dirname(file)
    dir_name = dir_name.replace('/mnt/sda/images/', '')  # dir_name relative to /mnt/sda/images/
    with open(file, 'r') as f:
        for line in f:
            if line.startswith('image_'):
                continue
            parts = line.strip().split(',')
            if not len(parts) == 3 or not parts[1] or not parts[2]:
                continue
            image_files.append(os.path.join(dir_name, parts[1]))

print(f'Found {len(image_files)} labels.')
image_files = sorted(set(image_files))  # Remove duplicates and sort
print(f'Found {len(image_files)} unique image files.')

with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
    f.write('\n'.join(image_files))

print('Uploading images...')
rclone.copy(
    '/mnt/sda/images/',
    f'wasabi-eu:helio.vanasseldonkmushrooms/images/{location}',
    args=['--files-from', f.name],
    show_progress=True,
)
